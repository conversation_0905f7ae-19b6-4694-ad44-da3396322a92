/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

import OpenAI from "openai";
import {AiChat, type AiChatRequest, type AiChatResponse} from "./types";
import {aiIntegrationsConfig} from "./config";
import {McpServer} from "@/ai/mcp-server.ts";

/**
 * The OpenAI model to use.
 *
 * See https://platform.openai.com/docs/models.
 */
const MODEL = "gpt-4.1-nano";

/**
 * An AI chat implemented through integration with the OpenAI API.
 */
export class OpenAiChat extends AiChat {

    private readonly client: OpenAI;
    private readonly mcpServer: McpServer;

    /**
     * Creates a new instance of this chat.
     *
     * @param apiKey the OpenAI API key to use.
     */
    constructor(apiKey: string) {
        super();
        this.client = new OpenAI({
            apiKey: apiKey,
            dangerouslyAllowBrowser: true
        });
        this.mcpServer = new McpServer(
            aiIntegrationsConfig.mcpServerLabel,
            aiIntegrationsConfig.mcpServerUrl
        );
    }

    /**
     * Submits the request to the OpenAI API and returns the response.
     *
     * Specifies the Progressor MCP server to be used for the processing of
     * the tenant-specific requests.
     *
     * The request metadata is passed to the MCP server as a custom "x-meta" header.
     */
    async chat(request: AiChatRequest): Promise<AiChatResponse> {
        try {
            console.log("Processing OpenAI chat request...");
            const response = await this.process(request);
            if (response.error) {
                return {
                    error: response.error.message
                };
            }
            response.output.forEach(
                output => console.log(`Output: ${output.type}`)
            );
            return {
                text: response.output_text || ""
            };
        } catch (error: any) {
            return {
                error: error.message
            };
        }
    }

    private async process(request: AiChatRequest) {
        const prompt = request.prompt;
        const mcpServerLabel = this.mcpServer.label;
        const mcpServerUrl = await this.mcpServer.publicUrl();

        /* eslint-disable camelcase */
        const mcpTool: any = {
            type: "mcp",
            server_label: mcpServerLabel,
            server_url: mcpServerUrl,
            require_approval: "never",
        };
        /* eslint-enable camelcase */

        if (request._meta) {
            mcpTool.headers = {
                "x-meta": JSON.stringify(request._meta)
            };
        }

        return this.client.responses.create({
            model: MODEL,
            tools: [mcpTool],
            input: prompt,
        });
    }
}
