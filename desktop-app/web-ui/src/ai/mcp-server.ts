/*
 * Copyright 2025 TeamDev. All rights reserved.
 * TeamDev PROPRIETARY and CONFIDENTIAL.
 * Use is subject to license terms.
 */

/**
 * A wrapper around the Progressor MCP server.
 *
 * See `mcp-server/README.md` for more details.
 */
export class McpServer {

    /**
     * The public URl of this server, fetched from its `/publicUrl` endpoint.
     *
     * The publicly available server will just return its own URL.
     * The local server will make itself available publicly via ngrok
     * and return the corresponding URL.
     */
    private _publicUrl: string | null = null;

    /**
     * Creates a new instance of the MCP server.
     *
     * @param serverLabel the label of the server to communicate to the LLM.
     * @param baseUrl the base URL of the server.
     */
    constructor(private readonly serverLabel: string,
                private readonly baseUrl: string) {
        this.fetchPublicUrl().then(url => {
            this._publicUrl = url;
        }).catch(error => {
            console.error("Failed to initialize the MCP server URL:", error);
        });
    }

    /**
     * The label of this server to identify it to the LLM.
     */
    get label(): string {
        return this.serverLabel;
    }

    /**
     * The public URL of this server.
     */
    async publicUrl(): Promise<string> {
        if (this._publicUrl) {
            return this._publicUrl;
        }
        return await this.fetchPublicUrl();
    }

    /**
     * Fetches the MCP server's public URL from its `/publicUrl` endpoint.
     *
     * The publicly available server will just return its own URL.
     * The local server will make itself available publicly via ngrok
     * and return the corresponding URL.
     */
    private async fetchPublicUrl(): Promise<string> {
        const response = await fetch(`${this.baseUrl}/publicUrl`);
        if (!response.ok) {
            throw new Error(
                `Failed to fetch MCP server URL: ${response.status} ${response.statusText}.`
            );
        }
        const data = await response.json();
        return data.publicUrl;
    }
}
